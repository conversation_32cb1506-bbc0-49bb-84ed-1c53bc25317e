name: temp Build Executables

on:
  workflow_dispatch:  # 手动触发工作流

jobs:
  build-windows:
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.x'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install -r requirements.txt
        
    - name: Build EXE
      run: |
        pyinstaller CursorKeepAlive.spec
        
    - name: Upload Windows artifact
      uses: actions/upload-artifact@v4
      with:
        name: CursorPro-Windows
        path: dist/CursorPro.exe

  build-macos-arm64:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.x'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install -r requirements.txt
        
    - name: Build MacOS ARM executable
      run: |
        pyinstaller CursorKeepAlive.spec
        
    - name: Upload MacOS ARM artifact
      uses: actions/upload-artifact@v4
      with:
        name: CursorPro-MacOS-ARM64
        path: dist/CursorPro

  build-linux:
    runs-on: ubuntu-22.04
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.x'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pyinstaller
        pip install -r requirements.txt
        
    - name: Build Linux executable
      run: |
        pyinstaller CursorKeepAlive.spec
        
    - name: Upload Linux artifact
      uses: actions/upload-artifact@v4
      with:
        name: CursorPro-Linux
        path: dist/CursorPro

  build-macos-intel:
    runs-on: macos-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.x'

    - name: Install dependencies
      run: |
        arch -x86_64 pip3 install --upgrade pip
        arch -x86_64 pip3 install pyinstaller
        arch -x86_64 pip3 install -r requirements.txt

    - name: Build MacOS Intel executable
      env:
        TARGET_ARCH: 'x86_64'
      run: |
        arch -x86_64 python3 -m PyInstaller CursorKeepAlive.spec
        
    - name: Upload MacOS Intel artifact
      uses: actions/upload-artifact@v4
      with:
        name: CursorPro-MacOS-Intel
        path: dist/CursorPro