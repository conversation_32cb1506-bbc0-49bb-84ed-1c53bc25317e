name: Remove old artifacts

on:
  schedule:
    # Every day at 1am
    - cron: '0 1 * * *'
  # 手动
  workflow_dispatch:


jobs:
  remove-old-artifacts:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Remove old artifacts
      uses: c-hive/gha-remove-artifacts@v1
      with:
        GITHUB_TOKEN: ${{ secrets.TOKEN }}
        age: '5 days' # '<number> <unit>', e.g. 5 days, 2 years, 90 seconds, parsed by Moment.js
        # Optional inputs
        # skip-tags: true
        # skip-recent: 5