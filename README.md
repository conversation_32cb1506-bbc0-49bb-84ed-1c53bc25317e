# Cursor Pro 自动化工具使用说明


[English doc](./README.EN.md)


## 在线文档
[cursor-auto-free-doc.vercel.app](https://cursor-auto-free-doc.vercel.app)


## 公众号 回复 1 获取 qq 群

![公众号](./screen/qrcode_for_gh_c985615b5f2b_258.jpg)

## 英文名字集
https://github.com/toniprada/usa-names-dataset

## 许可证声明
本项目采用 [CC BY-NC-ND 4.0](https://creativecommons.org/licenses/by-nc-nd/4.0/) 许可证。
这意味着您可以：
- 分享 — 在任何媒介以任何形式复制、发行本作品
但必须遵守以下条件：
- 非商业性使用 — 您不得将本作品用于商业目的

## 声明
- 本项目仅供学习交流使用，请勿用于商业用途。
- 本项目不承担任何法律责任，使用本项目造成的任何后果，由使用者自行承担。



## 骗子
海豚


## 感谢 linuxDo 这个开源社区(一个真正的技术社区)
https://linux.do/

## 特别鸣谢
本项目的开发过程中得到了众多开源项目和社区成员的支持与帮助，在此特别感谢：

### 开源项目
- [go-cursor-help](https://github.com/yuaotian/go-cursor-help) - 一个优秀的 Cursor 机器码重置工具，本项目的机器码重置功能使用该项目实现。该项目目前已获得 9.1k Stars，是最受欢迎的 Cursor 辅助工具之一。

## 请我喝杯茶 | buy me a cup of tea
<img src="./screen/image.png" width="300"/>
<img src="./screen/28613e3f3f23a935b66a7ba31ff4e3f.jpg" width="300"/>
 <img src="./screen/mm_facetoface_collect_qrcode_1738583247120.png" width="300"/>


